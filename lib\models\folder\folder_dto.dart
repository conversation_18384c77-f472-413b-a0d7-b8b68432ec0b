// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'folder_dto.freezed.dart';

part 'folder_dto.g.dart';

@freezed
class FolderDto with _$FolderDto {
  const factory FolderDto({
    @Default('') String id,
    @JsonKey(name: 'folder_name') @Default('') String folderName,
    @JsonKey(name: 'parent_folder_id') String? parentFolderId,
    @Default('') String path,
    @Default(0) int level,
    @J<PERSON><PERSON>ey(name: 'note_count') @Default(0) int noteCount,
    @Default(<FolderDto>[]) List<FolderDto> subfolders,
  }) = _FolderDto;

  factory FolderDto.fromJson(Map<String, dynamic> json) =>
      _$FolderDtoFromJson(json);
}
