// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_tab_setting_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CustomTabSettingState {
  List<TabType> get selectedTabs => throw _privateConstructorUsedError;
  List<TabType> get allTabs => throw _privateConstructorUsedError;
  List<TabType> get alwaysSelected => throw _privateConstructorUsedError;
  bool get isShowUpdateDialog => throw _privateConstructorUsedError;
  bool get isSaveButtonEnabled => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CustomTabSettingStateCopyWith<CustomTabSettingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomTabSettingStateCopyWith<$Res> {
  factory $CustomTabSettingStateCopyWith(CustomTabSettingState value,
          $Res Function(CustomTabSettingState) then) =
      _$CustomTabSettingStateCopyWithImpl<$Res, CustomTabSettingState>;
  @useResult
  $Res call(
      {List<TabType> selectedTabs,
      List<TabType> allTabs,
      List<TabType> alwaysSelected,
      bool isShowUpdateDialog,
      bool isSaveButtonEnabled});
}

/// @nodoc
class _$CustomTabSettingStateCopyWithImpl<$Res,
        $Val extends CustomTabSettingState>
    implements $CustomTabSettingStateCopyWith<$Res> {
  _$CustomTabSettingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTabs = null,
    Object? allTabs = null,
    Object? alwaysSelected = null,
    Object? isShowUpdateDialog = null,
    Object? isSaveButtonEnabled = null,
  }) {
    return _then(_value.copyWith(
      selectedTabs: null == selectedTabs
          ? _value.selectedTabs
          : selectedTabs // ignore: cast_nullable_to_non_nullable
              as List<TabType>,
      allTabs: null == allTabs
          ? _value.allTabs
          : allTabs // ignore: cast_nullable_to_non_nullable
              as List<TabType>,
      alwaysSelected: null == alwaysSelected
          ? _value.alwaysSelected
          : alwaysSelected // ignore: cast_nullable_to_non_nullable
              as List<TabType>,
      isShowUpdateDialog: null == isShowUpdateDialog
          ? _value.isShowUpdateDialog
          : isShowUpdateDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      isSaveButtonEnabled: null == isSaveButtonEnabled
          ? _value.isSaveButtonEnabled
          : isSaveButtonEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomTabSettingStateImplCopyWith<$Res>
    implements $CustomTabSettingStateCopyWith<$Res> {
  factory _$$CustomTabSettingStateImplCopyWith(
          _$CustomTabSettingStateImpl value,
          $Res Function(_$CustomTabSettingStateImpl) then) =
      __$$CustomTabSettingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TabType> selectedTabs,
      List<TabType> allTabs,
      List<TabType> alwaysSelected,
      bool isShowUpdateDialog,
      bool isSaveButtonEnabled});
}

/// @nodoc
class __$$CustomTabSettingStateImplCopyWithImpl<$Res>
    extends _$CustomTabSettingStateCopyWithImpl<$Res,
        _$CustomTabSettingStateImpl>
    implements _$$CustomTabSettingStateImplCopyWith<$Res> {
  __$$CustomTabSettingStateImplCopyWithImpl(_$CustomTabSettingStateImpl _value,
      $Res Function(_$CustomTabSettingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTabs = null,
    Object? allTabs = null,
    Object? alwaysSelected = null,
    Object? isShowUpdateDialog = null,
    Object? isSaveButtonEnabled = null,
  }) {
    return _then(_$CustomTabSettingStateImpl(
      selectedTabs: null == selectedTabs
          ? _value._selectedTabs
          : selectedTabs // ignore: cast_nullable_to_non_nullable
              as List<TabType>,
      allTabs: null == allTabs
          ? _value._allTabs
          : allTabs // ignore: cast_nullable_to_non_nullable
              as List<TabType>,
      alwaysSelected: null == alwaysSelected
          ? _value._alwaysSelected
          : alwaysSelected // ignore: cast_nullable_to_non_nullable
              as List<TabType>,
      isShowUpdateDialog: null == isShowUpdateDialog
          ? _value.isShowUpdateDialog
          : isShowUpdateDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      isSaveButtonEnabled: null == isSaveButtonEnabled
          ? _value.isSaveButtonEnabled
          : isSaveButtonEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CustomTabSettingStateImpl implements _CustomTabSettingState {
  const _$CustomTabSettingStateImpl(
      {final List<TabType> selectedTabs = const [],
      final List<TabType> allTabs = const [],
      final List<TabType> alwaysSelected = const [],
      this.isShowUpdateDialog = false,
      this.isSaveButtonEnabled = false})
      : _selectedTabs = selectedTabs,
        _allTabs = allTabs,
        _alwaysSelected = alwaysSelected;

  final List<TabType> _selectedTabs;
  @override
  @JsonKey()
  List<TabType> get selectedTabs {
    if (_selectedTabs is EqualUnmodifiableListView) return _selectedTabs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTabs);
  }

  final List<TabType> _allTabs;
  @override
  @JsonKey()
  List<TabType> get allTabs {
    if (_allTabs is EqualUnmodifiableListView) return _allTabs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allTabs);
  }

  final List<TabType> _alwaysSelected;
  @override
  @JsonKey()
  List<TabType> get alwaysSelected {
    if (_alwaysSelected is EqualUnmodifiableListView) return _alwaysSelected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_alwaysSelected);
  }

  @override
  @JsonKey()
  final bool isShowUpdateDialog;
  @override
  @JsonKey()
  final bool isSaveButtonEnabled;

  @override
  String toString() {
    return 'CustomTabSettingState(selectedTabs: $selectedTabs, allTabs: $allTabs, alwaysSelected: $alwaysSelected, isShowUpdateDialog: $isShowUpdateDialog, isSaveButtonEnabled: $isSaveButtonEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomTabSettingStateImpl &&
            const DeepCollectionEquality()
                .equals(other._selectedTabs, _selectedTabs) &&
            const DeepCollectionEquality().equals(other._allTabs, _allTabs) &&
            const DeepCollectionEquality()
                .equals(other._alwaysSelected, _alwaysSelected) &&
            (identical(other.isShowUpdateDialog, isShowUpdateDialog) ||
                other.isShowUpdateDialog == isShowUpdateDialog) &&
            (identical(other.isSaveButtonEnabled, isSaveButtonEnabled) ||
                other.isSaveButtonEnabled == isSaveButtonEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_selectedTabs),
      const DeepCollectionEquality().hash(_allTabs),
      const DeepCollectionEquality().hash(_alwaysSelected),
      isShowUpdateDialog,
      isSaveButtonEnabled);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomTabSettingStateImplCopyWith<_$CustomTabSettingStateImpl>
      get copyWith => __$$CustomTabSettingStateImplCopyWithImpl<
          _$CustomTabSettingStateImpl>(this, _$identity);
}

abstract class _CustomTabSettingState implements CustomTabSettingState {
  const factory _CustomTabSettingState(
      {final List<TabType> selectedTabs,
      final List<TabType> allTabs,
      final List<TabType> alwaysSelected,
      final bool isShowUpdateDialog,
      final bool isSaveButtonEnabled}) = _$CustomTabSettingStateImpl;

  @override
  List<TabType> get selectedTabs;
  @override
  List<TabType> get allTabs;
  @override
  List<TabType> get alwaysSelected;
  @override
  bool get isShowUpdateDialog;
  @override
  bool get isSaveButtonEnabled;
  @override
  @JsonKey(ignore: true)
  _$$CustomTabSettingStateImplCopyWith<_$CustomTabSettingStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
