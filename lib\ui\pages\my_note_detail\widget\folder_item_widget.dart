import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:note_x/lib.dart';

class FolderItemWidget extends StatelessWidget {
  final FolderModel folder;
  final String noteId;
  final String allNoteId;
  final VoidCallback onTap;

  const FolderItemWidget({
    super.key,
    required this.folder,
    required this.noteId,
    required this.allNoteId,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        // Border radius cho hiệu ứng tap
        child: ValueListenableBuilder(
          valueListenable: HiveService().noteBox.listenable(),
          builder: (context, Box<NoteModel> box, child) {
            final note = box.get(noteId);
            final isAllNotesFolder = folder.id == allNoteId;
            final belongsToFolder = note?.folderId == folder.backendId;
            final isUncategorized = note?.folderId.isEmpty == true;

            return Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: (isAllNotesFolder &&
                            (belongsToFolder || isUncategorized)) ||
                        belongsToFolder
                    ? context.colorScheme.mainSecondary
                    : context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Row(
                children: [
                  Container(
                    width: context.isTablet ? 32 : 32.w,
                    height: context.isTablet ? 32 : 32.w,
                    decoration: BoxDecoration(
                      color: context.colorScheme.mainPrimary.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    padding: EdgeInsets.all(context.isTablet ? 6 : 6.w),
                    child: SvgPicture.asset(
                      isReverseView()
                          ? Assets.icons.icFolderMini
                          : Assets.icons.icFolderMini,
                      height: context.isTablet ? 24 : 20.h,
                      width: context.isTablet ? 24 : 20.w,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainGray,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  AppConstants.kSpacingItemW8,
                  Expanded(
                    child: CommonText(
                      isCheckBaseLine: context.isLandscape ? false : true,
                      folder.folderName,
                      style: TextStyle(
                        fontSize: context.isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w500,
                        color: context.colorScheme.mainPrimary,
                      ),
                    ),
                  ),
                  if ((isAllNotesFolder &&
                          (belongsToFolder || isUncategorized)) ||
                      belongsToFolder)
                    SvgPicture.asset(
                      isReverseView()
                          ? Assets.icons.icFlipCreateNoteDone
                          : Assets.icons.icCreateNoteDone,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainBlue,
                        BlendMode.srcIn,
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
