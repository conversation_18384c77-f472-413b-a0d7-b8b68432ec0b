import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class MoveToFolderHelper {
  static void showBottomSheet(
    BuildContext context, {
    List<String>? foldersToBeMovedIds,
    String? jumpToFolderId,
    List<NoteModel>? notesToBeMoved,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) {
        return BlocProvider(
          create: (context) => GetIt.instance.get<MoveFolderCubit>()
            ..initialize(jumpToFolderId: jumpToFolderId),
          child: BlocConsumer<MoveFolderCubit, MoveFolderState>(
            listener: (context, state) {
              if (state.oneShotEvent == MoveFolderOneShotEvent.folderMoved) {
                Navigator.pop(context);
              } else if (state.oneShotEvent == MoveFolderOneShotEvent.subfolderCreated) {
                Navigator.pop(context);
              } else if (state.oneShotEvent == MoveFolderOneShotEvent.error) {
                // Show error message if needed
                debugPrint('MoveFolderCubit error: ${state.errorMessage}');
              }
            },
            builder: (context, state) {
              if (state.isLoading) {
                return const SafeArea(
                  child: SizedBox(
                    height: 200,
                    child: Center(child: CircularProgressIndicator()),
                  ),
                );
              }

              return StatefulBuilder(
                builder: (context, setState) {
                  return SafeArea(
                    child: Container(
                      height: context.isTablet ? 600 : 600.h,
                      padding: EdgeInsets.all(context.isTablet ? 24 : 16.w),
                      child: Column(
                        children: [
                          Text(
                            S.current.move,
                            style: context.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: context.isTablet ? 16 : 16.h),
                          Expanded(
                            child: ListView(
                              shrinkWrap: true,
                              children: [
                                ...state.rootFolders.map((folder) => MoveFolderTile(
                                      folder: folder,
                                      onTap: (f) {
                                        context.read<MoveFolderCubit>().selectFolder(f);
                                      },
                                      onToggleExpansion: (folderId) {
                                        context.read<MoveFolderCubit>().toggleFolderExpansion(folderId);
                                      },
                                    )),
                              ],
                            ),
                          ),
                          SizedBox(height: context.isTablet ? 16 : 16.h),
                          Row(
                            children: [
                              Expanded(
                                child: AppCommonButton(
                                  backgroundColor: context.colorScheme.mainSecondary,
                                  borderRadius: BorderRadius.circular(24.r),
                                  height: context.isTablet ? 44 : 44.h,
                                  onPressed: state.selectedFolder == null
                                      ? null
                                      : () {
                                          Navigator.pop(
                                              context); // Close bottom sheet first
                                          final controller =
                                              TextEditingController();
                                          showCreateFolderDialog(
                                            context,
                                            controller: controller,
                                            onPressed: () async {
                                              await context
                                                  .read<MoveFolderCubit>()
                                                  .createSubfolder(
                                                name: controller.text.trim(),
                                                parentFolderId: state.selectedFolder!
                                                    .backendId,
                                              );
                                              Navigator.pop(context);
                                            },
                                            onClosed: () {
                                              controller.dispose();
                                            },
                                            title: S.current.create_new_folder,
                                            contentButton: S.current.create,
                                            hintText: S.current.required,
                                            initialValue: '',
                                          );
                                        },
                                  textWidget: Text(
                                    S.current.create_new_folder,
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      color: context.colorScheme.onSecondary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: context.isTablet ? 12 : 8.w),
                              Expanded(
                                child: AppCommonButton(
                                  backgroundColor: context.colorScheme.mainBlue,
                                  borderRadius: BorderRadius.circular(24.r),
                                  height: context.isTablet ? 44 : 44.h,
                                  onPressed: state.selectedFolder == null
                                      ? null
                                      : () async {
                                          await context
                                              .read<MoveFolderCubit>()
                                              .moveFoldersAndNotes(
                                            folderIds: foldersToBeMovedIds ?? [],
                                            notes: notesToBeMoved ?? [],
                                            targetFolderId: state.selectedFolder!.backendId,
                                          );
                                        },
                                  textWidget: Text(
                                    S.current.move,
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      color: context.colorScheme.onPrimary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class MoveFolderTile extends StatelessWidget {
  final MoveFolderViewModel folder;
  final Function(MoveFolderViewModel folder)? onTap;
  final Function(String folderId)? onToggleExpansion;
  final int level;

  const MoveFolderTile({
    Key? key,
    required this.folder,
    this.onTap,
    this.onToggleExpansion,
    this.level = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        final isSelected = state.selectedFolder?.backendId == folder.backendId;

        return Column(
          children: [
            ListTile(
              contentPadding: EdgeInsets.only(left: 16.w * level),
              tileColor: isSelected ? context.colorScheme.mainSecondary : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.r),
              ),
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (folder.subfolders.isNotEmpty)
                    GestureDetector(
                      onTap: () => onToggleExpansion?.call(folder.backendId),
                      child: SvgPicture.asset(
                        folder.isExpanded
                            ? Assets.icons.icExpandMore
                            : Assets.icons.icExpandLess,
                        width: context.isTablet ? 24 : 16.w,
                        height: context.isTablet ? 24 : 16.w,
                        fit: BoxFit.contain,
                      ),
                    ),
                  SvgPicture.asset(
                    Assets.icons.icFlipFolderMini,
                    width: context.isTablet ? 48 : 32.w,
                    height: context.isTablet ? 48 : 32.w,
                    fit: BoxFit.contain,
                  ),
                ],
              ),
              title: Text(folder.folderName),
              onTap: () {
                onTap?.call(folder);
                if (folder.subfolders.isNotEmpty) {
                  onToggleExpansion?.call(folder.backendId);
                }
              },
            ),
            if (folder.isExpanded)
              ...folder.subfolders.map((sub) => MoveFolderTile(
                    folder: sub,
                    onTap: onTap,
                    onToggleExpansion: onToggleExpansion,
                    level: level + 1,
                  )),
          ],
        );
      },
    );
  }
}
