import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class EmptyNotePortrait extends StatefulWidget {
  const EmptyNotePortrait({
    super.key,
  });

  @override
  State<EmptyNotePortrait> createState() => _EmptyNotePortraitState();
}

class _EmptyNotePortraitState extends State<EmptyNotePortrait> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (GetIt.instance.get<AppCubit>().isUserFree()) ...[
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: context.isTablet ? 24 : 0,
                ),
                child: const PurchaseBannerSliderWidget(),
              ),
            ],
            AppConstants.kSpacingItem65,
            SvgPicture.asset(
                g.get<AppCubit>().isNewUser
                    ? Assets.icons.icEmptyNoteNewUser
                    : Assets.icons.icEmptyNoteNotexEmpty,
                width: 140,
                height: 140),
            AppConstants.kSpacingItem12,
            CommonText(
              g.get<AppCubit>().isNewUser
                  ? S.current.lets_create_your_first_ai_note
                  : S.current.notex_empty,
              style: TextStyle(
                fontSize: context.isTablet ? 22 : 20.sp,
                fontWeight: FontWeight.w700,
                color: context.colorScheme.mainPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            AppConstants.kSpacingItem8,
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: _defaultTextStyle(context),
                children: [
                  _normalTextSpan(context, S.current.tap_the_record),
                  _iconSpan(Assets.icons.icRecord),
                  _normalTextSpan(context, S.current.button_below),
                ],
              ),
            ),
            AppConstants.kSpacingItem120,
          ],
        ),
      ),
    );
  }

  TextStyle _defaultTextStyle(BuildContext context) => TextStyle(
        fontSize: context.isTablet ? 17 : 15.sp,
        fontWeight: FontWeight.w400,
        color: context.colorScheme.mainGray,
        fontFamily: AppConstants.fontSFPro,
      );

  TextSpan _normalTextSpan(BuildContext context, String text) =>
      TextSpan(text: " $text ");

  WidgetSpan _iconSpan(String asset) => WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: SvgPicture.asset(
          asset,
          width: 20,
          height: 20,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainPrimary,
            BlendMode.srcIn,
          ),
        ),
      );
}
