import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:pull_down_button/pull_down_button.dart';

class ItemQuizStep extends StatefulWidget {
  final List<QuizSetDto>? quizSets;
  final VoidCallback? onRefresh;

  const ItemQuizStep({
    super.key,
    this.quizSets,
    this.onRefresh,
  });

  @override
  State<ItemQuizStep> createState() => _ItemQuizStepState();
}

class _ItemQuizStepState extends State<ItemQuizStep> {
  TextEditingController editNameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    if (widget.quizSets == null || widget.quizSets!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Use Column instead of ListView for better nesting inside SingleChildScrollView
    return Column(
      children: List.generate(widget.quizSets!.length, (index) {
        final quizSetList = widget.quizSets![index];

        return GestureDetector(
          onTap: () {
            _chooseQuizSet(quizSetList.setId, context);
          },
          child: Padding(
            padding: EdgeInsets.only(
                bottom: index < widget.quizSets!.length - 1 ? 12.h : 0),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.r),
                color: context.colorScheme.mainNeutral,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonText(
                          quizSetList.name,
                          style: TextStyle(
                            color: context.colorScheme.mainPrimary,
                            fontWeight: FontWeight.w500,
                            fontSize: context.isTablet ? 16 : 14.sp,
                          ),
                          maxLines: 2,
                        ),
                        AppConstants.kSpacingItem4,
                        CommonText(
                          MyUtils.formatDateTimes(quizSetList.createdAt),
                          style: TextStyle(
                            fontSize: context.isTablet ? 15 : 12.sp,
                            fontWeight: FontWeight.w400,
                            color:
                                context.colorScheme.mainGray.withOpacity(0.6),
                          ),
                        ),
                        AppConstants.kSpacingItem8,
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 6.h,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primaryBlue.withOpacity(0.24),
                                borderRadius: BorderRadius.circular(100.r),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    width: 11.w,
                                    height: 11.h,
                                    Assets.icons.icNumberQuestion,
                                    colorFilter: const ColorFilter.mode(
                                      AppColors.gradientCtaLightBlueStart,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW4,
                                  CommonText(
                                    '${quizSetList.questionCount} ${quizSetList.questionCount == 1 ? 'Card' : 'Cards'}',
                                    height: 1,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: context.isTablet ? 14 : 12.sp,
                                      color: AppColors.primaryBlue,
                                    ),
                                  )
                                ],
                              ),
                            ),
                            AppConstants.kSpacingItemW8,
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 6.h,
                              ),
                              decoration: BoxDecoration(
                                color: _getDifficultyBackgroundColor(
                                  quizSetList.difficulty,
                                ),
                                borderRadius: BorderRadius.circular(100.r),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    width: 11.w,
                                    height: 11.h,
                                    Assets.icons.icDifficulty,
                                    colorFilter: ColorFilter.mode(
                                      _getDifficultyColor(
                                          quizSetList.difficulty),
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW4,
                                  CommonText(
                                    MyUtils.formatDifficultyText(
                                        quizSetList.difficulty),
                                    height: 1,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: context.isTablet ? 14 : 12.sp,
                                      color: _getDifficultyColor(
                                          quizSetList.difficulty),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  QuizSetsOptions(
                    isInDetailPage: true,
                    editNameController: editNameController,
                    quizSet: quizSetList,
                    onDeleteSuccess: widget.onRefresh,
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  void _chooseQuizSet(String setId, BuildContext context) async {
    final cubit = context.read<MyNoteDetailCubit>();
    final success = await cubit.chooseQuizSet(setId);
    if (success) {
      if (widget.onRefresh != null) {
        widget.onRefresh!();
      }
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();
    }
  }

  // Thêm helper method để lấy màu
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return AppColors.primaryGreen;
      case 'medium':
        return AppColors.primaryYellow;
      case 'hard':
        return AppColors.primaryRed;
      default:
        return AppColors.primaryBlue;
    }
  }

  // Thêm helper method để lấy opacity color
  Color _getDifficultyBackgroundColor(String difficulty) {
    return _getDifficultyColor(difficulty).withOpacity(0.24);
  }
}

class QuizSetsOptions extends StatelessWidget {
  final TextEditingController editNameController;
  final bool isInDetailPage;
  final QuizSetDto? quizSet;
  final Function? onDeleteSuccess;

  const QuizSetsOptions({
    Key? key,
    required this.editNameController,
    required this.isInDetailPage,
    this.quizSet,
    this.onDeleteSuccess,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<MyNoteDetailCubit>();

    return PullDownButton(
      routeTheme: PullDownMenuRouteTheme(
        backgroundColor: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(16.r),
        width: context.isTablet ? 200 : 200.w,
      ),
      itemBuilder: (context) => [
        PullDownMenuItem(
          title: S.current.edit_name,
          itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
            color: context.colorScheme.mainPrimary,
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
          )),
          onTap: () async {
            if (quizSet != null) {
              editNameController.text = quizSet!.name;
              showEditNameDialog(
                context,
                controller: editNameController,
                onPressed: () async {
                  if (editNameController.text.trim().isNotEmpty) {
                    final success = await cubit.editQuizSet(
                        quizSet!.setId, editNameController.text.trim());
                    if (success) {
                      if (onDeleteSuccess != null) {
                        onDeleteSuccess!(); // Use the same callback for refresh
                      }
                      _editQuizSet();
                      log('Quiz set name updated successfully');
                    } else {
                      log('Failed to update quiz set name');
                    }
                  }
                },
                title: S.current.edit_name,
                contentButton: S.current.change,
                hintText: S.current.enter_new_name,
                initialValue: quizSet!.name,
              );
            }
          },
          iconWidget: SvgPicture.asset(
            Assets.icons.icEditReminderTitle,
            width: 18.w,
            height: 18.h,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
        PullDownMenuItem(
          title: S.current.export_quiz,
          itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
            color: context.colorScheme.mainPrimary,
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
          )),
          onTap: () async {
            showModalBottomSheetExport(
              context,
              ExportType.quiz,
              Func1(
                (formatType) {
                  cubit.handleExportQuizSet(
                    formatType,
                    cubit.appCubit.isTablet
                        ? Rect.fromCenter(
                            center: Offset(
                              MediaQuery.of(context).size.width / 2,
                              MediaQuery.of(context).size.height / 2,
                            ),
                            width: 100,
                            height: 100,
                          )
                        : null,
                    quizSet?.name ?? '',
                    quizSet?.setId ?? '',
                    ExportType.quiz,
                  );
                },
              ),
              cubit.loadingNoteStates,
            );
          },
          iconWidget: SvgPicture.asset(
            Assets.icons.icDownload,
            width: 18.w,
            height: 18.h,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
            color: context.colorScheme.mainPrimary,
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
          )),
          onTap: () {
            CommonDialogs.buildDeleteDialog(
              context,
              title: S.current.delete_this_item,
              content: S.current.you_will_not_be,
              headerImageAssetFile: Assets.icons.icMascottDelete,
              onPressedDeleteButton: ([_]) async {
                if (quizSet != null) {
                  final success = await cubit.deleteQuizSet(quizSet!.setId);
                  if (success) {
                    if (onDeleteSuccess != null) {
                      onDeleteSuccess!();
                    }
                    _deleteQuizSet();
                    log('Quiz set deleted successfully');
                  } else {
                    log('Failed to delete Quiz set');
                  }
                }
              },
              onPressedCancelButton: () {
                //No action needed
              },
            );
          },
          title: S.current.delete,
          isDestructive: true,
          iconWidget: SvgPicture.asset(
            Assets.icons.icDelete,
            colorFilter: const ColorFilter.mode(
              AppColors.primaryRed,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
      buttonBuilder: (context, showMenu) {
        return GestureDetector(
          onTap: () {
            showMenu();
          },
          child: SvgPicture.asset(
            Assets.icons.icMore,
            width: context.isTablet ? 24 : 24.w,
            height: context.isTablet ? 24 : 24.w,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        );
      },
    );
  }

  void _deleteQuizSet() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.quiz_sets_scr_delete_success,
    );
  }

  void _editQuizSet() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.quiz_sets_scr_edit_name_success,
    );
  }
}
