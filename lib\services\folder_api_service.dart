import 'package:note_x/lib.dart';

abstract class FolderApiService {
  Future<FolderModel> createFolder(String folderName);

  Future<FolderModel> editFolder(String folderId, String folderName);

  Future<bool> deleteFolder({
    required String folderId,
  });
  Future<List<FolderModel>> getAllFolders();
  
  Future<FolderModel> createSubFolder(String name, String parentFolderId);
  
  Future<void> moveFolderToFolder(String folderId, String targetFolderId);

  Future<void> bulkDeleteItems({
    required List<String> folderIds,
    required List<String> noteIds,
    required bool deleteNotes,
  });

  Future<void> bulkMoveNotesToFolder({
    required String targetFolderId,
    required List<String> folderIds,
    required List<String> noteIds,
  });
}

class FolderApiServiceImpl extends BaseApiService implements FolderApiService {
  @override
  Future<FolderModel> createFolder(String folderName) async {
    try {
      await ensureConnection();
      final Map<String, dynamic> requestData = {
        "folder_name": folderName,
      };
      final response = await dio.post('v1/folders/', data: requestData);
      FolderDto folderDto = FolderDto.fromJson(response.data);
      FolderModel folder = FolderDataMapper().mapToEntity(folderDto);
      return folder;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<FolderModel> editFolder(String folderId, String folderName) async {
    try {
      await ensureConnection();
      final Map<String, dynamic> requestData = {
        "folder_name": folderName,
      };
      final response = await dio.patch(
        'v1/folders/$folderId',
        data: requestData,
      );
      FolderDto folderDto = FolderDto.fromJson(response.data);
      FolderModel folder = FolderDataMapper().mapToEntity(folderDto);
      return folder;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<bool> deleteFolder({required String folderId}) async {
    try {
      await ensureConnection();
      final response = await dio.delete('v1/folders/$folderId');
      return response.statusCode == 200;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<List<FolderModel>> getAllFolders() async {
    final response = await dio.get('v1/user/folders');
    final data = BaseDataListDto<FolderDto>.fromJson(response.data,
        (json) => FolderDto.fromJson(json as Map<String, dynamic>));
    
    return data.data.map((e) => FolderDataMapper().mapToEntity(e)).toList();
  }

  @override
  Future<FolderModel> createSubFolder(String name, String parentFolderId) async {
    try {
      await ensureConnection();
      final Map<String, dynamic> requestData = {
        "folder_name": name,
        "parent_folder_id": parentFolderId,
      };
      final response = await dio.post('v1/folders/', data: requestData);
      FolderDto folderDto = FolderDto.fromJson(response.data);
      FolderModel folder = FolderDataMapper().mapToEntity(folderDto);
      return folder;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<void> moveFolderToFolder(String folderId, String parentFolderId) async {
    try {
      await ensureConnection();
      final Map<String, dynamic> requestData = {
        "parent_folder_id": parentFolderId,
      };
      await dio.patch('v1/folders/$folderId/move', data: requestData);
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<void> bulkDeleteItems({
    required List<String> folderIds,
    required List<String> noteIds,
    required bool deleteNotes,
  }) async {
    try {
      await ensureConnection();
      final Map<String, dynamic> requestData = {
        "folder_ids": folderIds,
        "note_ids": noteIds,
        "delete_notes": deleteNotes,
      };
      await dio.post('v2/folders/bulk-delete', data: requestData);
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<void> bulkMoveNotesToFolder({
    required String targetFolderId,
    required List<String> folderIds,
    required List<String> noteIds,
  }) async {
    try {
      await ensureConnection();
      final Map<String, dynamic> requestData = {
        "folder_ids": folderIds,
        "note_ids": noteIds,
      };
      await dio.post('v2/folders/bulk-move/$targetFolderId', data: requestData);
    } catch (err) {
      throw getError(err);
    }
  }
}
