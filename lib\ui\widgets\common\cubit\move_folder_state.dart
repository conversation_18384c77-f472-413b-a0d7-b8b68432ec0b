import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';
import '../models/move_folder_view_model.dart';

part 'move_folder_state.freezed.dart';

@freezed
class MoveFolderState extends BaseState with _$MoveFolderState {
  const factory MoveFolderState({
    @Default(MoveFolderOneShotEvent.initial) MoveFolderOneShotEvent oneShotEvent,
    @Default([]) List<MoveFolderViewModel> rootFolders,
    MoveFolderViewModel? selectedFolder,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _MoveFolderState;
}

enum MoveFolderOneShotEvent { 
  initial, 
  loading, 
  success, 
  error,
  folderMoved,
  subfolderCreated,
}
