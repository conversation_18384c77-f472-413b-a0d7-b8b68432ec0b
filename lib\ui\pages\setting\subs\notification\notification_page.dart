import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:flutter_svg/svg.dart';

import 'cubit/notification_state.dart';

class NotificationPage extends StatefulWidget {
  static const String routeName = 'NotificationPage';

  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState
    extends BasePageStateDelegate<NotificationPage, NotificationCubit>
    with WidgetsBindingObserver {
  TextEditingController titleEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    cubit.initialize();
    cubit.reloadData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      cubit.reloadData();
    }
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<NotificationCubit, NotificationState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != NotificationOneShotEvent.none,
      listener: (context, state) async {
        switch (state.oneShotEvent) {
          case NotificationOneShotEvent.none:
            break;
          case NotificationOneShotEvent.createNewOne:
            titleEditingController.text = cubit.currentNotificationTitle;
            _showReminderUpdateDialog(
                context,
                titleEditingController,
                ReminderUpdateType.create,
                cubit.currentNotificationDateTime,
                cubit.currentNotificationDaysOfWeek);
            break;
          case NotificationOneShotEvent.recreateNewOne:
            _showReminderUpdateDialog(
                context,
                titleEditingController,
                ReminderUpdateType.create,
                cubit.currentNotificationDateTime,
                cubit.currentNotificationDaysOfWeek);
            break;
          case NotificationOneShotEvent.edit:
            titleEditingController.text = cubit.currentNotificationTitle;
            _showReminderUpdateDialog(
                context,
                titleEditingController,
                ReminderUpdateType.edit,
                cubit.currentNotificationDateTime,
                cubit.currentNotificationDaysOfWeek);
            break;
          case NotificationOneShotEvent.delete:
            _showDeleteReminderDialog(context);
            break;
          case NotificationOneShotEvent.discardChanges:
            _showDiscardChangeDialog(context);
            break;
          case NotificationOneShotEvent.requestForPermission:
            _showNotiPermissionRequestDialog(context);
            break;
        }
        cubit.resetOneShotEvent();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: S.current.recording_schedule,
        backgroundColor: context.colorScheme.mainBackground,
        onPressed: () {
          cubit.onNotificationBack();
          Navigator.pop(context);
        },
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.current.notifications_note_ready,
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        S.current.notifications_note_created,
                        style:
                            const TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                  BlocBuilder<NotificationCubit, NotificationState>(
                    buildWhen: (previous, current) =>
                        current.isNoteReadyNotificationEnabled !=
                        previous.isNoteReadyNotificationEnabled,
                    builder: (context, state) {
                      return CupertinoSwitch(
                        activeColor: context.colorScheme.mainBlue,
                        trackColor: context.colorScheme.mainGray,
                        thumbColor: Colors.white,
                        value: state.isNoteReadyNotificationEnabled,
                        onChanged: (value) {
                          cubit.onNoteReadySwitched();
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
            SizedBox(height: 20.h),
            BlocBuilder<NotificationCubit, NotificationState>(
              buildWhen: (previous, current) =>
                  current.isNotificationEnabled !=
                  previous.isNotificationEnabled,
              builder: (context, state) {
                return Opacity(
                  opacity: state.isNotificationEnabled ? 1 : 0.38,
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              S.current.recording_schedule,
                              textAlign: TextAlign.start,
                              style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                  color: context.colorScheme.mainPrimary),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              S.current.reminders_record_audio,
                              textAlign: TextAlign.start,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: context.colorScheme.mainGray),
                            )
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          cubit.onCreateNewReminder();
                        },
                        child: SvgPicture.asset(
                          Assets.icons.icCircleAdd,
                          width: 24.w,
                          height: 24.h,
                          colorFilter: ColorFilter.mode(
                            context.colorScheme.mainPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            SizedBox(height: 20.h),
            // Reminder Cards
            BlocBuilder<NotificationCubit, NotificationState>(
              buildWhen: (previous, current) =>
                  current.reminderModels != previous.reminderModels ||
                  current.isNotificationEnabled !=
                      previous.isNotificationEnabled,
              builder: (context, state) {
                return Expanded(
                  child: ListView.builder(
                    itemCount: state.isNotificationEnabled
                        ? state.reminderModels.length
                        : 0,
                    itemBuilder: (context, index) {
                      final reminderModel = state.reminderModels[index];
                      return ReminderCard(
                        model: reminderModel,
                        onDeleting: cubit.onDeleting,
                        onEditing: cubit.onEditing,
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showReminderUpdateDialog(
      BuildContext context,
      TextEditingController titleEditingController,
      ReminderUpdateType type,
      DateTime selectedDatetime,
      List<DayOfWeek> selectedDays) {
    showReminderUpdateDialog(context, onPressed: () {
      cubit.onUpdateReminder(titleEditingController.text, type);
    }, onTimeChanged: (datetime) {
      cubit.onTimeChanged(datetime);
    }, onClosed: () {
      cubit.onReminderDialogCancel(type);
    }, onDaySelectionChanged: (days) {
      cubit.onDaySelectionChanged(days);
    },
        hintText: '',
        type: type,
        selectedDatetime: selectedDatetime,
        titleController: titleEditingController,
        selectedDays: selectedDays);
  }

  void _showDiscardChangeDialog(BuildContext context) {
    showNewCupertinoDialog(
        context: context,
        title: S.current.discard_changes,
        message: S.current.content_discard_changes_reminder,
        image: Assets.icons.icDiscardChanges,
        cancelButton: S.current.cancel,
        confirmButton: S.current.discard,
        onCancel: () {
          cubit.onCancelDiscardChanges();
        },
        onConfirm: () {});
  }

  void _showDeleteReminderDialog(BuildContext context) {
    CommonDialogs.buildDeleteDialog(context,
        title: S.current.delete_reminder,
        content: S.current.content_delete_reminder,
        headerImageAssetFile: Assets.icons.icMascottDelete,
        onPressedCancelButton: () {}, onPressedDeleteButton: ([_]) {
      cubit.onUpdateReminder(
          titleEditingController.text, ReminderUpdateType.delete);
    });
  }

  void _showNotiPermissionRequestDialog(BuildContext context) {
    showBlackCupertinoDialog(
        context: context,
        title: S.current.noti_req_title,
        message: S.current.noti_req_description,
        cancelButton: S.current.donotallow,
        confirmButton: S.current.allow,
        confirmButtonTextColor: context.colorScheme.mainBlue,
        onCancel: () {},
        onConfirm: () {
          cubit.onAllowNotiPermissionRequest();
        });
  }
}
