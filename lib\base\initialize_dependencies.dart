import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/document/cubit/cubit/document_cubit_state.dart';
import 'package:note_x/ui/pages/chat/cubit/chat_state.dart';
import 'package:note_x/ui/pages/folder/cubit/folder_state.dart';
import 'package:note_x/ui/pages/folder/cubit/root_folders_cubit.dart';
import 'package:note_x/ui/pages/home/<USER>/home_state.dart';
import 'package:note_x/ui/pages/login/cubit/login_state.dart';
import 'package:note_x/ui/pages/onboarding/cubit/onboarding_state.dart';
import 'package:note_x/ui/pages/purchase/cubit/purchase_state.dart';
import 'package:note_x/ui/pages/purchase_life_time/cubit/purchase_life_time_state.dart';
import 'package:note_x/ui/pages/quiz/cubit/quiz_state.dart';
import 'package:note_x/ui/pages/record_app/cubit/record_app_state.dart';
import 'package:note_x/ui/pages/referral/cubit/referral_state.dart';
import 'package:note_x/ui/pages/search/cubit/search_state.dart';
import 'package:note_x/ui/pages/setting/subs/notification/cubit/notification_state.dart';
import 'package:note_x/ui/pages/shorts_detail/short_details_state.dart';
import 'package:note_x/ui/pages/slide_show/cubit/slide_show_cubit_state.dart';
import 'package:note_x/ui/pages/slide_show_create/cubit/slide_show_create_cubit_state.dart';
import 'package:note_x/ui/pages/summary/cubit/summary_page_state.dart';
import 'package:note_x/ui/pages/upload_text/cubit/upload_text_state.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../ui/pages/camera/cubit/camera_state.dart';
import '../ui/pages/create_shorts/cubit/create_shorts_state.dart';
import '../ui/pages/my_note_detail/cubit/my_note_detail_state.dart';
import '../ui/pages/purchase_promotion/cubit/purchase_promotion_state.dart';
import '../ui/pages/setting/cubit/setting_state.dart';
import '../ui/pages/upload_audio/cubit/upload_audio_state.dart';
import '../ui/pages/upload_document/cubit/upload_document_state.dart';
import '../ui/pages/upload_image/cubit/upload_image_state.dart';
import '../ui/pages/youtube/cubit/youtube_state.dart';
import 'app/app_state.dart';

final g = GetIt.I;

Future initializeDependencies() async {
  g.registerSingleton(NavigationService());
  Dio dio = Dio(BaseOptions(
      baseUrl: Env.instance.baseURL,
      connectTimeout: const Duration(seconds: 15)));
  dio.interceptors.add(
    Oauth2Interceptor(dio: dio),
  );
  if (!kReleaseMode) {
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
        enabled: kDebugMode,
        filter: (options, args) {
          // don't print requests with uris containing '/posts'
          if (options.path.contains('/posts') ||
              // options.path.contains('/user/notes') ||
              options.path.contains('/community') ||
              options.path.contains('/v1/note')) {
            return false;
          }
          // don't print responses with unit8 list data
          return !args.isResponse || !args.hasUint8ListData;
        },
      ),
    );
  }
  g.registerLazySingleton(() => dio, instanceName: Env.instance.baseURL);

  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  g.registerSingleton(FirebaseNotificationService());
  //region Local Service - Hive
  HiveService().init();
  await LocalModule.initModule();
  g.registerSingleton(await SharedPreferences.getInstance());
  g.registerSingleton(LocalService());
  g.registerLazySingleton(() => ReferralApiServiceImpl());
  g.registerLazySingleton(() => CreateNoteApiServiceImpl());
  g.registerLazySingleton(() => CreditApiServiceImpl());
  g.registerLazySingleton(() => AuthApiServiceImpl());
  g.registerLazySingleton(() => FolderApiServiceImpl());
  g.registerLazySingleton(() => GetNoteApiServiceImpl());
  g.registerLazySingleton<QuizApiService>(() => QuizApiServiceImpl());
  g.registerLazySingleton<FlashCardApiService>(() => FlashCardApiServiceImpl());
  g.registerLazySingleton<MindMapApiService>(() => MindMapApiServiceImpl());
  g.registerLazySingleton(() => ResultTaskApiServiceImpl());
  g.registerLazySingleton(() => SlideShowApiServiceImpl());
  g.registerLazySingleton(() => CreateShortsApiServiceImpl());
  g.registerLazySingleton(() => CacheService());
  g.registerLazySingleton(() => RevenueCatService());
  g.registerLazySingleton(() => BackgroundVideoApiServiceImpl());
  g.registerLazySingleton(() => ChatApiServiceImpl());
  g.registerLazySingleton(() => EmailLinkAuthService());
  g.registerLazySingleton(() => AppCubit(const AppState()));
  g.registerFactory(() => HomeCubit(const HomeState()));
  g.registerFactory<QuizCubit>(() => QuizCubit(const QuizState()));
  g.registerFactory(() => MyNoteDetailCubit(const MyNoteDetailState()));
  g.registerFactory<OnboardingCubit>(
      () => OnboardingCubit(const OnboardingState()));
  g.registerFactory<UploadAudioCubit>(
      () => UploadAudioCubit(const UploadAudioState()));
  g.registerFactory<YoutubeCubit>(() => YoutubeCubit(const YoutubeState()));
  g.registerFactory<RecordAppCubit>(
      () => RecordAppCubit(const RecordAppState()));
  g.registerFactory<PurchaseCubit>(() => PurchaseCubit(const PurchaseState()));
  g.registerFactory<PurchaseLifeTimeCubit>(
      () => PurchaseLifeTimeCubit(const PurchaseLifeTimeState()));
  g.registerFactory<SettingCubit>(() => SettingCubit(const SettingState()));
  g.registerFactory<CustomTabSettingCubit>(() => CustomTabSettingCubit());
  g.registerFactory<NotificationCubit>(
      () => NotificationCubit(const NotificationState()));
  g.registerFactory<SummaryPageCubit>(
      () => SummaryPageCubit(const SummaryPageState()));
  g.registerSingleton<ChatCubit>(ChatCubit(const ChatState()));
  g.registerFactory<FolderDetailCubit>(
      () => FolderDetailCubit(const FolderState()));
  g.registerFactory<RootFoldersCubit>(
      () => RootFoldersCubit(const FolderState()));
  g.registerFactory<MoveFolderCubit>(() => MoveFolderCubit());
  g.registerFactory<LoginCubit>(() => LoginCubit(const LoginState()));
  g.registerFactory<AccountPageCubit>(() => AccountPageCubit());
  g.registerLazySingleton<FileUploadServiceImpl>(() => FileUploadServiceImpl());
  g.registerFactory<SearchCubit>(() => SearchCubit(const SearchState()));

  g.registerFactory<PurchasePromotionCubit>(
      () => PurchasePromotionCubit(const PurchasePromotionState()));
  g.registerFactory<ReferralCubit>(() => ReferralCubit(const ReferralState()));

  final localService = g<LocalService>();
  final themeId = await localService.getThemeId();
  final initialTheme = AppThemeConst.getById(themeId);
  g.registerSingleton<ThemeNotifier>(ThemeNotifier(initialTheme));

  g.registerFactory<ShortsDetailCubit>(
      () => ShortsDetailCubit(const ShortDetailsState()));

  g.registerFactory<CreateShortsCubit>(
      () => CreateShortsCubit(const CreateShortsState()));
  g.registerFactory<PurchaseLifeTimeCubitOldVer>(
      () => PurchaseLifeTimeCubitOldVer(const PurchaseLifeTimeState()));

  g.registerFactory<UploadTextCubit>(
      () => UploadTextCubit(const UploadTextState()));
  g.registerFactory<UploadImageCubit>(
      () => UploadImageCubit(const UploadImageState()));
  g.registerFactory<CameraCubit>(() => CameraCubit(const CameraState()));

  g.registerFactory<UploadDocumentCubit>(
      () => UploadDocumentCubit(const UploadDocumentState()));
  g.registerFactory<SlideShowCubit>(
      () => SlideShowCubit(const SlideShowCubitState()));
  g.registerFactory<SlideShowCreateCubit>(
      () => SlideShowCreateCubit(const SlideShowCreateCubitState()));
  g.registerFactory<DocumentCubit>(
      () => DocumentCubit(const DocumentCubitState()));
  await initPurchaseConfig();

  g.registerLazySingleton(() => FolderService());
}
