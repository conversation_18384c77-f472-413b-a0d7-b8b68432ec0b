import 'package:note_x/base/base.dart';
import 'base_data_mapper.dart';

class FolderDataMapper extends BaseDataMapper<FolderDto, FolderModel> {
  @override
  FolderModel mapToEntity(FolderDto data) {
    return FolderModel(
      id: data.id,
      backendId: data.id,
      folderName: data.folderName,
      icon: '', // Default value - not provided in API response
      createdAt: '', // Default value - not provided in API response
      updatedAt: '', // Default value - not provided in API response
      parentFolderId: data.parentFolderId,
      path: data.path,
      level: data.level,
      noteCount: data.noteCount,
      subfolders: data.subfolders.map((subfolder) => mapToEntity(subfolder)).toList(),
    );
  }

  @override
  FolderDto mapToDto(FolderModel entity) {
    return FolderDto(
      id: entity.backendId.isNotEmpty ? entity.backendId : entity.id,
      folderName: entity.folderName,
      parentFolderId: entity.parentFolderId,
      path: entity.path,
      level: entity.level,
      noteCount: entity.noteCount,
      subfolders: entity.subfolders.map((subfolder) => mapToDto(subfolder)).toList(),
    );
  }
}
