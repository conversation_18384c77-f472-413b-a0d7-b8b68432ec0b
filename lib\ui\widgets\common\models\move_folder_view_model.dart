import 'package:freezed_annotation/freezed_annotation.dart';

part 'move_folder_view_model.freezed.dart';

@freezed
class MoveFolderViewModel with _$MoveFolderViewModel {
  const factory MoveFolderViewModel({
    required String id,
    required String backendId,
    required String folderName,
    required String icon,
    required String createdAt,
    required String updatedAt,
    String? parentFolderId,
    @Default([]) List<MoveFolderViewModel> subfolders,
    required String path,
    required int level,
    required int noteCount,
    @Default(false) bool isSelected,
    @Default(false) bool isExpanded,
  }) = _MoveFolderViewModel;

  // Factory constructor to create from FolderModel
  factory MoveFolderViewModel.fromFolderModel(
    dynamic folderModel, {
    bool isSelected = false,
    bool isExpanded = false,
  }) {
    return MoveFolderViewModel(
      id: folderModel.id,
      backendId: folderModel.backendId,
      folderName: folderModel.folderName,
      icon: folderModel.icon,
      createdAt: folderModel.createdAt,
      updatedAt: folderModel.updatedAt,
      parentFolderId: folderModel.parentFolderId,
      subfolders: (folderModel.subfolders as List)
          .map((sub) => MoveFolderViewModel.fromFolderModel(sub))
          .toList(),
      path: folderModel.path,
      level: folderModel.level,
      noteCount: folderModel.noteCount,
      isSelected: isSelected,
      isExpanded: isExpanded,
    );
  }
}
