import 'dart:math';

import 'package:hive/hive.dart';
import 'package:note_x/lib.dart';
import 'package:uuid/uuid.dart';

part 'folder_model.g.dart';

@HiveType(typeId: HiveTypeId.folderTypeId)
class FolderModel extends HiveObject {
  @HiveField(0, defaultValue: '')
  final String id;

  @HiveField(1, defaultValue: '')
  String folderName;

  @HiveField(2, defaultValue: '')
  String backendId;

  @HiveField(3, defaultValue: '')
  String icon;

  @HiveField(4, defaultValue: '')
  String createdAt;

  @HiveField(5, defaultValue: '')
  String updatedAt;

  @HiveField(6, defaultValue: null)
  String? parentFolderId;

  @HiveField(7, defaultValue: [])
  List<FolderModel> subfolders;

  @HiveField(8, defaultValue: '')
  String path;

  @HiveField(9, defaultValue: 0)
  int level;

  @HiveField(10, defaultValue: 0)
  int noteCount;

  static const int maxLevel = 4;

  FolderModel({
    String? id,
    required this.folderName,
    required this.backendId,
    String? icon,
    String? createdAt,
    String? updatedAt,
    String? parentFolderId,
    List<FolderModel>? subfolders,
    String? path,
    int? level,
    int? noteCount,
  })  : id = id ?? const Uuid().v4(),
        icon = icon ?? '',
        createdAt = createdAt ?? DateTime.now().toIso8601String(),
        updatedAt = updatedAt ?? DateTime.now().toIso8601String(),
        parentFolderId = parentFolderId ?? '',
        subfolders = subfolders ?? [],
        path = path ?? _generatePath(parentFolderId, folderName),
        level = level ?? _calculateLevel(parentFolderId),
        noteCount = noteCount ?? 0;

  static String _generatePath(String? parentFolderId, String folderName) {
    // This will be updated when we have the parent context
    return '/$folderName';
  }

  static int _calculateLevel(String? parentFolderId) {
    // This will be calculated based on parent level
    return parentFolderId == null ? 0 : 1;
  }

  FolderModel copyWith({
    String? id,
    String? folderName,
    String? backendId,
    String? icon,
    String? createdAt,
    String? updatedAt,
    String? parentFolderId,
    List<FolderModel>? subfolders,
    String? path,
    int? level,
    int? noteCount,
  }) {
    return FolderModel(
      id: this.id,
      folderName: folderName ?? this.folderName,
      backendId: backendId ?? this.backendId,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now().toIso8601String(),
      parentFolderId: parentFolderId ?? this.parentFolderId,
      subfolders: subfolders ?? this.subfolders,
      path: path ?? this.path,
      level: level ?? this.level,
      noteCount: noteCount ?? this.noteCount,
    );
  }

  // Helper methods
  bool get isRootFolder => parentFolderId == null && level == 0;

  bool get canHaveSubfolders => level < maxLevel;

  bool get hasSubfolders => subfolders.isNotEmpty;

  bool get hasNotes => noteCount > 0;

  // Add a subfolder
  bool addSubfolder(FolderModel subfolder) {
    if (!canHaveSubfolders) return false;
    if (subfolders.any((f) => f.id == subfolder.id)) return false;

    // Update subfolder's parent info
    final updatedSubfolder = subfolder.copyWith(
      parentFolderId: id,
      level: level + 1,
      path: '$path/${subfolder.folderName}',
    );

    subfolders.add(updatedSubfolder);
    save();
    return true;
  }

  // Remove a subfolder
  bool removeSubfolder(String subfolderId) {
    final index = subfolders.indexWhere((f) => f.id == subfolderId);
    if (index == -1) return false;

    subfolders.removeAt(index);
    save();
    return true;
  }

  // Get all descendants (recursive)
  List<FolderModel> getAllDescendants() {
    final result = <FolderModel>[];

    for (final subfolder in subfolders) {
      result.add(subfolder);
      result.addAll(subfolder.getAllDescendants());
    }

    return result;
  }

  // Find a folder by ID in the tree
  FolderModel? findFolderById(String folderId) {
    if (id == folderId) return this;

    for (final subfolder in subfolders) {
      final found = subfolder.findFolderById(folderId);
      if (found != null) return found;
    }

    return null;
  }

  // Update path recursively
  void updatePathRecursively(String newBasePath) {
    path = '$newBasePath/$folderName';

    for (final subfolder in subfolders) {
      subfolder.updatePathRecursively(path);
    }

    save();
  }

  // Get folder depth (how many subfolders deep)
  int get depth {
    if (subfolders.isEmpty) return 0;

    int maxDepth = 0;
    for (final subfolder in subfolders) {
      maxDepth = max(maxDepth, subfolder.depth + 1);
    }

    return maxDepth;
  }

  // Get total folder count (including all descendants)
  int get totalFolderCount {
    int count = 1; // Count this folder

    for (final subfolder in subfolders) {
      count += subfolder.totalFolderCount;
    }

    return count;
  }

  // Get all folders at a specific level
  List<FolderModel> getFoldersAtLevel(int targetLevel) {
    final result = <FolderModel>[];

    if (level == targetLevel) {
      result.add(this);
    }

    for (final subfolder in subfolders) {
      result.addAll(subfolder.getFoldersAtLevel(targetLevel));
    }

    return result;
  }

  // Search folders by name (recursive)
  List<FolderModel> searchByName(String query) {
    final result = <FolderModel>[];

    if (folderName.toLowerCase().contains(query.toLowerCase())) {
      result.add(this);
    }

    for (final subfolder in subfolders) {
      result.addAll(subfolder.searchByName(query));
    }

    return result;
  }

  // Get breadcrumb path as list of folder names
  List<String> getBreadcrumbs() {
    return path.split('/').where((part) => part.isNotEmpty).toList();
  }

  @override
  String toString() {
    return '''
FolderModel {
  id: $id,
  folderName: $folderName,
  backendId: $backendId,
  icon: $icon,
  level: $level,
  path: $path,
  parentFolderId: $parentFolderId,
  noteCount: $noteCount,
  subfolders: ${subfolders.length} items,
  canHaveSubfolders: $canHaveSubfolders,
  createdAt: ${createdAt.isNotEmpty ? createdAt : 'Not set'},
  updatedAt: ${updatedAt.isNotEmpty ? updatedAt : 'Not set'}
}''';
  }

  // Print folder tree structure
  String printTree({String indent = ''}) {
    final buffer = StringBuffer();
    buffer.writeln('$indent📁 $folderName (Level $level, Notes: $noteCount)');

    for (int i = 0; i < subfolders.length; i++) {
      final isLast = i == subfolders.length - 1;
      final childIndent = indent + (isLast ? '    ' : '│   ');
      final connector = isLast ? '└── ' : '├── ';

      buffer.write('$indent$connector');
      buffer.write(subfolders[i]
          .printTree(indent: childIndent)
          .substring(indent.length + connector.length));
    }

    return buffer.toString();
  }
}
